//
//  FAQService.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import Foundation

/// FAQ 服務錯誤類型
enum FAQError: Error {
    case networkError
    case invalidResponse
    case decodingError
    case noDataFound
    
    var message: String {
        switch self {
        case .networkError:
            return "網路連線錯誤"
        case .invalidResponse:
            return "無效的回應"
        case .decodingError:
            return "資料解析錯誤"
        case .noDataFound:
            return "找不到對應語言的 FAQ 資料"
        }
    }
}

/// FAQ 服務類別
class FAQService: ObservableObject {
    static let shared = FAQService()
    
    @Published var faqItems: [FAQItem] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let baseURL = "https://weather.minlsm.com/faq"

    /// 是否使用本地測試資料 (開發測試用)
    private let useLocalTestData = false

    private init() {}
    
    /// 根據語言代碼獲取 FAQ 資料
    /// - Parameter languageCode: 語言代碼 (例如: "zh-Hant", "en")
    func fetchFAQ(for languageCode: String) {
        isLoading = true
        errorMessage = nil

        // 如果使用本地測試資料
        if useLocalTestData {
            loadLocalTestData(for: languageCode)
            return
        }

        // let urlString = "\(baseURL)/\(languageCode).json"
        let urlString = "\(baseURL)/faq_data.json"
        
        guard let url = URL(string: urlString) else {
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = FAQError.invalidResponse.message
            }
            return
        }
        
        Logger.debug("FAQ API 請求: \(urlString)")
        
        URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
            DispatchQueue.main.async {
                guard let self = self else { return }
                
                self.isLoading = false
                
                if let error = error {
                    Logger.error("FAQ API 網路錯誤: \(error.localizedDescription)")
                    self.errorMessage = FAQError.networkError.message
                    return
                }
                
                guard let data = data else {
                    Logger.error("FAQ API: 沒有收到資料")
                    self.errorMessage = FAQError.invalidResponse.message
                    return
                }
                
                // 打印原始回應以便調試
                if let responseString = String(data: data, encoding: .utf8) {
                    Logger.debug("FAQ API 原始回應: \(responseString)")
                }
                
                do {
                    let faqDataArray = try JSONDecoder().decode([FAQData].self, from: data)
                    Logger.success("FAQ API 解析成功，語言數量: \(faqDataArray.count)")
                    
                    // 尋找對應語言的 FAQ 資料
                    if let targetFAQ = faqDataArray.first(where: { $0.language == languageCode }) {
                        self.faqItems = targetFAQ.faq
                        Logger.success("找到 \(languageCode) 語言的 FAQ，項目數量: \(targetFAQ.faq.count)")
                    } else {
                        // 如果找不到對應語言，嘗試使用英文作為後備
                        if let englishFAQ = faqDataArray.first(where: { $0.language == "en" }) {
                            self.faqItems = englishFAQ.faq
                            Logger.warning("找不到 \(languageCode) 語言的 FAQ，使用英文後備")
                        } else {
                            Logger.error("找不到任何可用的 FAQ 資料")
                            self.errorMessage = FAQError.noDataFound.message
                        }
                    }
                } catch {
                    Logger.error("FAQ API 資料解析錯誤: \(error)")
                    if let decodingError = error as? DecodingError {
                        Logger.error("解析錯誤詳情: \(decodingError)")
                    }
                    self.errorMessage = FAQError.decodingError.message
                }
            }
        }.resume()
    }
    
    /// 獲取當前語言代碼
    private func getCurrentLanguageCode() -> String {
        let currentLanguage = LanguageService.shared.currentLanguage
        
        // 將 LanguageService.Language 映射到 API 語言參數
        switch currentLanguage {
        case .english:
            return "en"
        case .traditionalChinese:
            return "zh-Hant"
        case .japanese:
            return "ja"
        case .australian:
            return "en"
        case .british:
            return "en"
        case .french:
            return "fr"
        case .frenchCanadian:
            return "fr"
        case .german:
            return "de"
        case .spanish:
            return "es"
        case .italian:
            return "it"
        case .dutch:
            return "nl"
        case .danish:
            return "da"
        case .swedish:
            return "sv"
        case .norwegian:
            return "no"
        case .finnish:
            return "fi"
        }
    }
    
    /// 載入當前語言的 FAQ
    func loadCurrentLanguageFAQ() {
        let languageCode = getCurrentLanguageCode()
        fetchFAQ(for: languageCode)
    }
    
    /// 清除資料
    func clearData() {
        faqItems = []
        errorMessage = nil
        isLoading = false
    }

    /// 載入本地測試資料
    /// - Parameter languageCode: 語言代碼
    private func loadLocalTestData(for languageCode: String) {
        Logger.debug("FAQ Service: 使用本地測試資料")

        guard let path = Bundle.main.path(forResource: "test_faq", ofType: "json"),
              let data = NSData(contentsOfFile: path) as Data? else {
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "找不到本地測試檔案"
            }
            return
        }

        do {
            let faqDataArray = try JSONDecoder().decode([FAQData].self, from: data)
            Logger.success("本地 FAQ 資料解析成功，語言數量: \(faqDataArray.count)")

            DispatchQueue.main.async {
                self.isLoading = false

                // 尋找對應語言的 FAQ 資料
                if let targetFAQ = faqDataArray.first(where: { $0.language == languageCode }) {
                    self.faqItems = targetFAQ.faq
                    Logger.success("找到 \(languageCode) 語言的本地 FAQ，項目數量: \(targetFAQ.faq.count)")
                } else {
                    // 如果找不到對應語言，嘗試使用英文作為後備
                    if let englishFAQ = faqDataArray.first(where: { $0.language == "en" }) {
                        self.faqItems = englishFAQ.faq
                        Logger.warning("找不到 \(languageCode) 語言的本地 FAQ，使用英文後備")
                    } else {
                        Logger.error("找不到任何可用的本地 FAQ 資料")
                        self.errorMessage = FAQError.noDataFound.message
                    }
                }
            }
        } catch {
            Logger.error("本地 FAQ 資料解析錯誤: \(error)")
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = FAQError.decodingError.message
            }
        }
    }
}
