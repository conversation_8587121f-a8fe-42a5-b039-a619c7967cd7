//
//  FAQData.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import Foundation

/// FAQ 資料模型
struct FAQData: Codable, Identifiable {
    let id = UUID()
    let language: String
    let faq: [FAQItem]
    
    private enum CodingKeys: String, CodingKey {
        case language, faq
    }
}

/// FAQ 項目模型
struct FAQItem: Codable, Identifiable {
    let id = UUID()
    let title: String
    let body: String
    
    private enum CodingKeys: String, CodingKey {
        case title, body
    }
}
