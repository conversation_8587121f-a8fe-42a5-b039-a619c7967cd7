//
//  SettingsPageView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import Combine
import AutoInch
import RevenueCat
import SwiftRater

struct SettingsPageView: View {
    // MARK: - 屬性
    @ObservedObject var viewModel: WeatherViewModel
    @ObservedObject private var iapService = IAPService.shared
    var scrollToTopAction: () -> Void
    @State private var isShowingForecast = false
    @State private var showingTemperatureUnitPicker = false
    @State private var tempSelectedUnit: TemperatureUnit = .celsius
    @State private var showingLanguageSelection = false
    @State private var showingTimeFormatSelection = false
    @State private var tempSelectedTimeFormat: TimeFormat = .twentyFourHour
    @State private var showingMeasurementSystemPicker = false
    @State private var tempSelectedMeasurementSystem: MeasurementSystem = .metric
    @State private var showingThemePicker = false
    @State private var tempSelectedTheme: ThemeMode = .light
    @State private var isShowingLocationSearch = false
    @State private var isShowingPaywall = false
    @State private var isShowingFaq = false
    @State private var isShowingWidgetSettings = false
    @State private var isShowingCustomWhatsNew = false
    @State private var isBreathing = false

    @State private var displayAreaPadding: CGFloat = CGFloat(60).auto()
    
    // MARK: - 視圖
    var body: some View {
        VStack(spacing: 0) {



            VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
                // 返回頂部按鈕
                HStack {
                    // Spacer()
                    Button {
                        scrollToTopAction()
                    } label: {
                        AppIconsSymbol.createView(for: AppIcons.scrollDown, fontSize: CGFloat(44).auto(), color: HexColor.themed(.secondaryText))
                    }
                }
                .padding(.top, CGFloat(50).auto())
                .frame(maxWidth: .infinity, alignment: .leading)                

                // 位置管理
                SettingsRow(title: "location".localized.uppercased()) {
                    isShowingLocationSearch = true
                }
                .padding(.top, CGFloat(60).auto())

                // 小工具設定
                SettingsRow(title: "widget_settings".localized.uppercased()) {
                    isShowingWidgetSettings = true
                }

                // What's New
                // SettingsRow(title: "whats_new".localized.uppercased()) {
                //     isShowingCustomWhatsNew = true
                // }

                // PRO 按鈕
                // SettingsRow(title: "pro".localized.uppercased()) {
                //     showPaywall()
                // }
                
                // SettingsRow(title: "feedback".localized.uppercased()) {
                //     // 打開 Feedback 網站
                //     if let url = URL(string: "https://minlsm.featurebase.app/") {
                //         UIApplication.shared.open(url)
                //     }
                // }
                
                
                // SettingsRow(title: "about".localized.uppercased()) {
                //     // 打開 About 網站
                //     if let url = URL(string: "https://www.minlsm.com/") {
                //         UIApplication.shared.open(url)
                //     }
                // }

                // 溫度單位設定
                SettingsRow(title: viewModel.temperatureUnit.displayName.uppercased()) {
                    tempSelectedUnit = viewModel.temperatureUnit
                    showingTemperatureUnitPicker = true
                }

                // 時間格式設定
                SettingsRow(title: viewModel.timeFormat.displayName.uppercased()) {
                    tempSelectedTimeFormat = viewModel.timeFormat
                    showingTimeFormatSelection = true
                }

                // 測量單位設定
                SettingsRow(title: viewModel.measurementSystem.displayName.uppercased()) {
                    tempSelectedMeasurementSystem = viewModel.measurementSystem
                    showingMeasurementSystemPicker = true
                }

                // 主題設定
                SettingsRow(title: viewModel.themeMode.displayName.uppercased()) {
                    tempSelectedTheme = viewModel.themeMode
                    showingThemePicker = true
                }

                // Debug 模式設定（開發者用）
                // #if DEBUG
                // SwiftRater 測試按鈕
                // SettingsRow(title: "TEST RATING PROMPT") {
                //     SwiftRater.rateApp(host: nil)
                // }

                // SettingsRow(title: "DEBUG PAYWALL: \(AppSettings.shared.paywallDebugMode ? "ON" : "OFF")") {
                //     AppSettings.shared.paywallDebugMode.toggle()
                // }

                // // 清除 Paywall 記錄（開發者用）
                // SettingsRow(title: "CLEAR PAYWALL HISTORY") {
                //     PaywallManager.shared.clearPaywallHistory()
                // }

                // // 顯示 Paywall Debug 資訊
                // SettingsRow(title: "PAYWALL DEBUG INFO") {
                //     print(PaywallManager.shared.getDebugInfo())
                // }

                // // 手動觸發 Paywall
                // SettingsRow(title: "SHOW PAYWALL NOW") {
                //     PaywallManager.shared.showPaywall()
                // }

                // // 執行 Paywall 測試
                // SettingsRow(title: "RUN PAYWALL TESTS") {
                //     PaywallManagerTests.runAllTests()
                // }
                // #endif

                Spacer() // 將社群圖示推到底部
                
                HStack(spacing: CGFloat(20).auto()) {
                    // Paywall Premium
                    Button {
                        showPaywall()
                    } label: {
                        if iapService.isPro {
                            AppIconsSymbol.createView(
                                for: AppIcons.paywall,
                                fontSize: 44,
                                color: HexColor.themed(.primaryText)
                            )
                        }
                        else {
                            ZStack {
                                AppIconsSymbol.createView(
                                    for: AppIcons.paywall,
                                    fontSize: 44,
                                    color: HexColor.themed(.premium)
                                )

                                Circle()
                                    .fill(Color.red.opacity(0.7))
                                    .frame(width: CGFloat(6).auto(), height: CGFloat(6).auto())
                                    .scaleEffect(isBreathing ? 1.4 : 1.0)
                                    .offset(x: CGFloat(14).auto())
                                    .offset(y: CGFloat(-14).auto())
                                    .animation(
                                        Animation.easeInOut(duration: 1.5)
                                            .repeatForever(autoreverses: true),
                                        value: isBreathing
                                    )
                                    .onAppear {
                                        isBreathing = true
                                    }
                            }
                        }                        
                    }

                    // FAQ
                    Button {
                        isShowingFaq = true
                    } label: {
                        AppIconsSymbol.createView(
                            for: AppIcons.faq,
                            fontSize: 44,
                            color: HexColor.themed(.primaryText)
                        )                        
                    }
                    
                    // 打開 Feedback 網站
                    Button {
                        if let url = URL(string: "https://minlsm.featurebase.app/") {
                            UIApplication.shared.open(url)
                        }
                    } label: {
                        AppIconsSymbol.createView(for: AppIcons.feedback, fontSize: 44, color: HexColor.themed(.primaryText))
                    }
                    
                    // 打開 About 網站
                    Button {
                        if let url = URL(string: "https://www.minlsm.com/") {
                            UIApplication.shared.open(url)
                        }
                    } label: {
                        AppIconsSymbol.createView(for: AppIcons.about, fontSize: 44, color: HexColor.themed(.primaryText))
                    }
                    
                    // 主題設定
                    // Button {
                    //     tempSelectedTheme = viewModel.themeMode
                    //     showingThemePicker = true
                    // } label: {
                    //     AppIconsSymbol.createView(for: getThemeIcon(for: viewModel.themeMode), fontSize: 44, color: HexColor.themed(.primaryText))
                    // }
                }
                .offset(x: CGFloat(-10).auto())
        
                .padding(.bottom, CGFloat(50).auto()) // 調整底部邊距
            }
        
        .background(HexColor.themed(.primaryBackground))
        .cornerRadius(10)
        .padding(.horizontal, displayAreaPadding)
        

        

        
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(HexColor.themed(.primaryBackground))
        
        .fullScreenCover(isPresented: $showingTemperatureUnitPicker) {
            TemperatureUnitPickerView(
                selectedUnit: $tempSelectedUnit,
                onDismiss: {
                    showingTemperatureUnitPicker = false
                },
                onConfirm: {
                    Logger.debug("設置溫度單位: \(tempSelectedUnit.displayName)")
                    viewModel.setTemperatureUnit(tempSelectedUnit)
                    viewModel.updateTemperatureDisplay() // 強制更新溫度顯示
                    showingTemperatureUnitPicker = false
                }
            )
        }
        .fullScreenCover(isPresented: $isShowingLocationSearch) {
            LocationSearchView(viewModel: viewModel, scrollToTopAction: scrollToTopAction)
        }
        .fullScreenCover(isPresented: $isShowingPaywall) {
            PaywallView()
        }
        .fullScreenCover(isPresented: $isShowingFaq) {
            FaqView()
        }
        .fullScreenCover(isPresented: $showingLanguageSelection) {
            LanguageSelectionView()
        }
        .fullScreenCover(isPresented: $showingTimeFormatSelection) {
            TimeFormatPickerView(
                selectedTimeFormat: $tempSelectedTimeFormat,
                onDismiss: {
                    showingTimeFormatSelection = false
                },
                onConfirm: {
                    Logger.debug("設置時間格式: \(tempSelectedTimeFormat.displayName)")
                    viewModel.setTimeFormat(tempSelectedTimeFormat)
                    viewModel.updateTimeDisplay() // 強制更新時間顯示
                    showingTimeFormatSelection = false
                }
            )
        }
        .fullScreenCover(isPresented: $showingMeasurementSystemPicker) {
            MeasurementSystemPickerView(
                selectedSystem: $tempSelectedMeasurementSystem,
                onDismiss: {
                    showingMeasurementSystemPicker = false
                },
                onConfirm: {
                    Logger.debug("設置測量單位: \(tempSelectedMeasurementSystem.displayName)")
                    viewModel.setMeasurementSystem(tempSelectedMeasurementSystem)
                    showingMeasurementSystemPicker = false
                }
            )
        }
        .fullScreenCover(isPresented: $showingThemePicker) {
            ThemePickerView(
                selectedTheme: $tempSelectedTheme,
                onDismiss: {
                    showingThemePicker = false
                },
                onConfirm: {
                    Logger.debug("設置主題模式: \(tempSelectedTheme.displayName)")
                    viewModel.setThemeMode(tempSelectedTheme)
                    showingThemePicker = false
                }
            )
        }
        .fullScreenCover(isPresented: $isShowingWidgetSettings) {
            WidgetLocationPickerView(viewModel: viewModel) {
                isShowingWidgetSettings = false
            }
        }
        .fullScreenCover(isPresented: $isShowingCustomWhatsNew) {
            CustomWhatsNewView(showWhatsNew: $isShowingCustomWhatsNew)
        }

    }
    
    // MARK: - IAP 相關方法
    private func showPaywall() {
        // 檢查是否已經是 Pro 用戶
        if iapService.isPro {
            isShowingPaywall = true
            // Logger.debug("用戶已經是 Pro 會員")
            // 可以顯示已購買的狀態或其他操作
            return
        }
        
        // 顯示 paywall
        isShowingPaywall = true
    }
    
    private func getThemeIcon(for theme: ThemeMode) -> String {
        switch theme {
        case .system:
            // 系統模式根據當前系統外觀決定圖標
            if #available(iOS 13.0, *) {
                return UITraitCollection.current.userInterfaceStyle == .dark ? AppIcons.themeday : AppIcons.themenight
            } else {
                return AppIcons.themenight
            }
        case .light:
            // 白天主題顯示夜晚圖標（表示可以切換到夜晚）
            return AppIcons.themenight
        case .dark:
            // 夜晚主題顯示白天圖標（表示可以切換到白天）
            return AppIcons.themeday
        }
    }
}

// MARK: - 設置列表行視圖
struct SettingsRow: View {
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Text(title)
                    .foregroundColor(HexColor.themed(.primaryText))
                    .font(.system(size: CGFloat(22).auto(), weight: .medium, design: .rounded))
                // Spacer()
                // Image(systemName: "chevron.right")
                //     .foregroundColor(.secondary)
            }
            // .padding(.vertical, CGFloat(0).auto())
            // .contentShape(Rectangle())
        }
        .buttonStyle(.plain)
    }
}



// MARK: - 預覽
#Preview {
    SettingsPageView(viewModel: WeatherViewModel(), scrollToTopAction: {})
}
