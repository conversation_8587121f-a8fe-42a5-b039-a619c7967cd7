//
//  OnboardingTermsPrivacyView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch

struct OnboardingTermsPrivacyView: View {
    let onNext: () -> Void
    let onPrevious: () -> Void
    @State private var isExanded1 = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
            
            
            // 標題
            Text("terms_privacy".localized)
                .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
                .padding(.top, CGFloat(50).auto())

            VStack(alignment: .leading, spacing: CGFloat(0).auto()) {
                DisclosureGroup(
                    isExpanded: $isExanded1,
                    content: {
                        VStack(alignment: .leading, spacing: CGFloat(0).auto()) {
                            Text("Temperature: 80F")
                                .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                                .foregroundColor(HexColor.themed(.secondaryText))
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.top, CGFloat(12).auto())
                    },
                    label: {
                        Button(action: {
                            withAnimation {
                                .toggle()
                            }
                        }) {
                            Text("Current Weather Details")
                                .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                                .foregroundColor(HexColor.themed(.primaryText))                      
                        }
                        .buttonStyle(PlainButtonStyle()) // 移除按鈕的預設樣式，確保自訂樣式能正確顯示 [4]
                    }
                )
                .accentColor(HexColor.themed(.primaryText)) // 更改 DisclosureGroup 的箭頭顏色為紅色 [2, 5]
            }

            // 連結區域
            // VStack(alignment: .leading, spacing: CGFloat(0).auto()) {
            //     // 隱私政策連結
            //     Button(action: {
            //         if let url = URL(string: "https://minlsm.featurebase.app/privacy") {
            //             UIApplication.shared.open(url)
            //         }
            //     }) {
            //         HStack(spacing: CGFloat(0).auto()) {
            //             AppIconsSymbol.createView(for: AppIcons.file, fontSize: CGFloat(44).auto(), color: HexColor.themed(.primaryText))

            //             Text("privacy_policy".localized)
            //                 .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
            //                 .foregroundColor(HexColor.themed(.primaryText))                      
            //         }
            //         .offset(x: CGFloat(-10).auto())
            //         // .padding(.vertical, CGFloat(16).auto())
            //     }
                
            //     // 使用條款連結
            //     Button(action: {
            //         if let url = URL(string: "https://minlsm.featurebase.app/terms") {
            //             UIApplication.shared.open(url)
            //         }
            //     }) {
            //         HStack(spacing: CGFloat(0).auto()) {
            //             AppIconsSymbol.createView(for: AppIcons.file, fontSize: CGFloat(44).auto(), color: HexColor.themed(.primaryText))

            //             Text("terms_of_service".localized)
            //                 .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
            //                 .foregroundColor(HexColor.themed(.primaryText))
            //         }
            //         .offset(x: CGFloat(-10).auto())
            //         // .padding(.vertical, CGFloat(16).auto())
            //     }
            // }
            .padding(.top, CGFloat(12).auto())
            // .padding(.horizontal, CGFloat(30).auto())
            
            Spacer()
            
            // 底部按鈕區域
            OnboardingBottomButtons(
                onNext: onNext,
                onPrevious: onPrevious,
                nextButtonText: "accept".localized,
                isNextDisabled: false,
                shouldShowPurchaseFlow: false
            )
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
    }
}

#Preview {
    OnboardingTermsPrivacyView(onNext: {}, onPrevious: {})
} 