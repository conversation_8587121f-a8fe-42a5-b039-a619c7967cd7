//
//  OnboardingCoordinator.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI

struct OnboardingCoordinator: View {
    @State private var currentStep: Int = 0
    @Binding var isOnboardingCompleted: Bool
    @Binding var shouldShowOnboarding: Bool
    
    private let totalSteps = 5
    private let locationRepository = LocationRepository()
    @ObservedObject private var iapService = IAPService.shared
    
    var body: some View {
        ZStack {
            HexColor.themed(.primaryBackground).edgesIgnoringSafeArea(.all)
            
            switch currentStep {
            case 0:
                OnboardingWelcomeView(
                    onNext: { handleWelcomeNext() }
                )
            case 1:
                OnboardingTermsPrivacyView(
                    onNext: { nextStep() },
                    onPrevious: { previousStep() }
                )
            // case 1:
            //     OnboardingFeaturePlanView(
            //         onNext: { smartNextStep() },
            //         onPrevious: { previousStep() }
            //     )
            case 2:
                OnboardingLocationView(
                    onNext: { nextStep() },
                    onPrevious: { previousStep() }
                )
            case 3:
                OnboardingCompleteView(
                    onComplete: { completeOnboarding() },
                    onPrevious: { previousStep() }
                )
            default:
                OnboardingWelcomeView(
                    onNext: { nextStep() }
                )
            }
        }
        .animation(.easeInOut(duration: 0.3), value: currentStep)
        .onAppear {
            // 檢查是否需要跳過位置設定步驟
            checkAndSkipLocationStepIfNeeded()
        }
    }
    
    private func nextStep() {
        if currentStep < totalSteps - 1 {
            currentStep += 1
        }
    }
    
    private func previousStep() {
        if currentStep > 0 {
            currentStep -= 1
        }
    }
    
    private func completeOnboarding() {
        UserDefaults.standard.set(true, forKey: "hasCompletedOnboarding")
        isOnboardingCompleted = true
        shouldShowOnboarding = false
    }
    
    /// 檢查是否需要跳過位置設定步驟
    private func checkAndSkipLocationStepIfNeeded() {
        let hasSavedLocations = !locationRepository.getAllSavedLocations().isEmpty
        
        if hasSavedLocations {
            Logger.debug("檢測到已有儲存位置，將跳過位置設定步驟")
            // 如果有儲存位置，我們需要調整步驟邏輯
            // 但保持 UI 流程一致，只是在到達位置步驟時自動跳過
        }
    }
    
    /// 處理歡迎頁面的下一步邏輯
    private func handleWelcomeNext() {
        let hasActiveSubscription = iapService.isEntitlementActive("pro")
        
        if hasActiveSubscription {
            // 有訂閱的用戶直接跳到最後一步（完成頁面）
            Logger.debug("檢測到活躍訂閱，直接跳到完成頁面")
            currentStep = 3 // 直接到完成步驟
        } else {
            // 免費用戶正常進行下一步
            nextStep()
        }
    }
    
    /// 智能下一步邏輯
    private func smartNextStep() {
        let hasSavedLocations = !locationRepository.getAllSavedLocations().isEmpty
        
        // 如果當前在方案選擇步驟(step 2)，且有儲存位置，直接跳到完成步驟
        if currentStep == 2  && hasSavedLocations {
            Logger.debug("有儲存位置，跳過位置設定步驟")
            currentStep = 3 // 直接到完成步驟
        } else {
            nextStep()
        }
    }
}

#Preview {
    OnboardingCoordinator(
        isOnboardingCompleted: .constant(false),
        shouldShowOnboarding: .constant(true)
    )
} 