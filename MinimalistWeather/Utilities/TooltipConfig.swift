//
//  TooltipConfig.swift
//  MinimalistWeather
//
//  Created by Ke<PERSON> on 2025/8/1.
//

import SwiftUI
import SwiftUITooltip
import AutoInch

/// Tooltip 配置工具類
/// 提供統一的 Tooltip 樣式和動畫配置，確保整個 App 的一致性
struct TooltipConfig {
    
    /// 預設的跳躍動畫 Tooltip 配置
    /// 用於維護狀態提示等需要吸引注意的場景
    static var jumpingAnimation: DefaultTooltipConfig {
        var config = DefaultTooltipConfig()
        config.enableAnimation = true
        config.animationOffset = 10
        config.animationTime = 1
        config.backgroundColor = HexColor.themed(.primaryText)
        config.borderColor = HexColor.themed(.primaryText)
        config.borderRadius = CGFloat(8).auto()
        config.contentPaddingLeft = CGFloat(8).auto()
        config.contentPaddingRight = CGFloat(8).auto()
        config.contentPaddingTop = CGFloat(4).auto()
        config.contentPaddingBottom = CGFloat(4).auto()
        return config
    }
    
    /// 標準 Tooltip 配置
    /// 用於一般資訊提示，無動畫效果
    static var standard: DefaultTooltipConfig {
        var config = DefaultTooltipConfig()
        config.enableAnimation = false
        config.backgroundColor = HexColor.themed(.primaryText)
        config.borderColor = HexColor.themed(.primaryText)
        config.borderRadius = CGFloat(8).auto()
        config.contentPaddingLeft = CGFloat(8).auto()
        config.contentPaddingRight = CGFloat(8).auto()
        config.contentPaddingTop = CGFloat(4).auto()
        config.contentPaddingBottom = CGFloat(4).auto()
        return config
    }
    
    /// 警告樣式 Tooltip 配置
    /// 用於錯誤或警告訊息，使用警告色彩
    static var warning: DefaultTooltipConfig {
        var config = DefaultTooltipConfig()
        config.enableAnimation = true
        config.animationOffset = 8
        config.animationTime = 0.8
        config.backgroundColor = HexColor.themed(.primaryText)
        config.borderColor = HexColor.themed(.primaryText)
        config.borderRadius = CGFloat(8).auto()
        config.contentPaddingLeft = CGFloat(8).auto()
        config.contentPaddingRight = CGFloat(8).auto()
        config.contentPaddingTop = CGFloat(4).auto()
        config.contentPaddingBottom = CGFloat(4).auto()
        return config
    }
}

/// Tooltip 內容建構器
/// 提供統一的 Tooltip 內容樣式
struct TooltipContent {
    
    /// 建立維護狀態的 Tooltip 內容
    /// - Parameter message: 維護訊息文字
    /// - Returns: 格式化的 Tooltip 內容視圖
    static func maintenance(_ message: String) -> some View {
        Text(message)
            .font(.system(size: CGFloat(12).auto(), weight: .medium))
            .foregroundColor(HexColor.themed(.primaryBackground))
    }
    
    /// 建立標準資訊的 Tooltip 內容
    /// - Parameter message: 資訊文字
    /// - Returns: 格式化的 Tooltip 內容視圖
    static func info(_ message: String) -> some View {
        Text(message)
            .font(.system(size: CGFloat(12).auto(), weight: .regular))
            .foregroundColor(HexColor.themed(.primaryBackground))
    }
    
    /// 建立警告訊息的 Tooltip 內容
    /// - Parameter message: 警告文字
    /// - Returns: 格式化的 Tooltip 內容視圖
    static func warning(_ message: String) -> some View {
        Text(message)
            .font(.system(size: CGFloat(12).auto(), weight: .medium))
            .foregroundColor(.white)
    }
}

/// View 擴展，提供便捷的 Tooltip 方法
extension View {
    
    /// 加入維護狀態的跳躍動畫 Tooltip
    /// - Parameters:
    ///   - side: Tooltip 顯示位置
    ///   - message: 維護訊息
    /// - Returns: 帶有 Tooltip 的視圖
    func maintenanceTooltip(_ side: TooltipSide = .top, message: String = "系統維護中，請稍後再試") -> some View {
        self.tooltip(side, config: TooltipConfig.jumpingAnimation) {
            TooltipContent.maintenance(message)
        }
    }
    
    /// 加入標準資訊 Tooltip
    /// - Parameters:
    ///   - side: Tooltip 顯示位置
    ///   - message: 資訊文字
    /// - Returns: 帶有 Tooltip 的視圖
    func infoTooltip(_ side: TooltipSide = .top, message: String) -> some View {
        self.tooltip(side, config: TooltipConfig.standard) {
            TooltipContent.info(message)
        }
    }
    
    /// 加入警告 Tooltip
    /// - Parameters:
    ///   - side: Tooltip 顯示位置
    ///   - message: 警告文字
    /// - Returns: 帶有 Tooltip 的視圖
    func warningTooltip(_ side: TooltipSide = .top, message: String) -> some View {
        self.tooltip(side, config: TooltipConfig.warning) {
            TooltipContent.warning(message)
        }
    }
}
