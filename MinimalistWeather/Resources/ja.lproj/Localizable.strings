/* 
  Localizable.strings (Japanese)
  MinimalistWeather
*/

// MARK: - Onboarding
"welcome" = "ようこそ";
"onboarding_title_before_begin" = "始める前に";
"terms_privacy" = "利用規約とプライバシーポリシー";
"privacy_policy" = "プライバシーポリシー";
"terms_of_service" = "利用規約";
"accept" = "OK、次へ";
"location" = "登録地点";
"add_forecasts_city" = "予報地点を追加";
"searching" = "検索中...";
"confirm_add" = "追加しますか？";
"add_more_cities" = "さらに地点を追加";
"choose_your_plan" = "プランを選択";
"feature_plans_available" = "機能プランはこちらで利用可能になります";
"coming_soon" = "近日公開予定";
"continue" = "続ける";
"all_set" = "設定が完了しました！";
"ready_to_start" = "さあ、はじめましょう！";
"get_started" = "はじめる";

// MARK: - Main App
"no_connection" = "オフライン";
"check_internet_connection" = "インターネット接続を確認してください";
"feedback" = "フィードバック";

// MARK: - FAQ
"faq_title" = "よくあるご質問";
"loading_faq" = "よくある質問を読み込み中...";
"error_loading_faq" = "よくある質問の読み込みエラー";
"retry" = "再試行";

// MARK: - Weather
"server_error" = "接続できませんでした";
"send_feedback_check_announcements" = "再読み込み、提供元の切り替え、または稼働状況の確認をお試しください。";
"status_check" = "稼働状況を確認";

// MARK: - Settings
"unit" = "表示単位";
"pro" = "プレミアム機能";
"language" = "言語";
"time_format" = "時刻の表示形式";
"12_hour_format" = "12時間表示";
"24_hour_format" = "24時間表示";

// MARK: - Location Search
"search_city_name" = "地名や住所で検索";
"no_locations_matched" = "一致する地点が見つかりませんでした";
"search_for_a_city" = "地点を検索";
"no_saved_locations" = "登録済みの地点がありません";
"unlimited_locations_available" = "プレミアム版で、地点登録が無制限に";
"upgrade_to_pro_add_more" = "アップグレードして地点を追加";

// MARK: - Paywall
"thanks_for_pro" = "プレミアムへのアップグレード、ありがとうございます！";
"pro_description" = "ご質問やご提案がございましたら、お気軽にお知らせください。一緒により良いアプリを作っていきましょう。";
"unlock_full_experience" = "すべての機能をアンロック";
"5_day_forecast" = "5日間予報";
"multiple_locations" = "複数地点の登録";
"all_future_features" = "今後の全機能を含む";
"purchasing" = "購入処理中...";
"upgrade" = "アップグレード";
"restoring" = "復元処理中...";
"restore" = "購入情報を復元";
"purchase_agreement" = "利用規約 | プライバシーポリシー";
"terms" = "利用規約";
"loading" = "読み込み中";

// MARK: - Common
"ok" = "OK";
"cancel" = "キャンセル";
"search" = "検索";
"close" = "閉じる";
"add" = "追加";
"delete" = "削除";
"edit" = "編集";
"retry" = "再試行";
"auto_retrying" = "再試行中...";
"change_weather_source" = "天気情報提供元を変更する";

// MARK: - Time
"am" = "午前";
"pm" = "午後";
"now" = "今";

// MARK: - Weekdays
"monday" = "月";
"tuesday" = "火";
"wednesday" = "水";
"thursday" = "木";
"friday" = "金";
"saturday" = "土";
"sunday" = "日";

// MARK: - Weather Units
"celsius" = "°C";
"fahrenheit" = "°F";
"percent" = "%";

// MARK: - 機能プラン (Features Plan)
"current_weather_forecast" = "現在の天気";
"3_hour_2_day_forecast" = "48時間先までの3時間ごと予報";
"1_location_forecast" = "1地点のみ登録可能";
"3_hour_5_day_forecast" = "5日先までの3時間ごと予報";
"2_location_forecast" = "最大50地点まで登録可能";
"detailed_weather_info" = "詳細な気象情報";
"custom_night_theme" = "ダークテーマ";
"no_ads" = "広告非表示";
"start_free" = "無料で始める";
"start_7_day_trial" = "3日間、無料でお試しできます！";
"monthly_plan" = "月間プラン";
"yearly_plan" = "年間プラン (-50%)";

// MARK: - Alerts
"no_connection_alert" = "オフラインです";
"connect_internet_message" = "気象情報を更新するには、インターネットに接続してください。";

// MARK: - IAP Upgrade
"upgrade_to_pro" = "プレミアムにアップグレード";
"upgrade_now" = "今すぐアップグレード";
"upgrade_message_multiple_locations" = "プレミアムにアップグレードして、複数地点の登録など、全ての機能をお楽しみください。";

// MARK: - Setup Completion Alert
"please_complete_setup" = "初期設定を完了してください";
"complete_setup_before_using" = "この機能を利用するには、初期設定を完了する必要があります。";
"go_to_setup" = "設定へ進む";

// MARK: - Subscription Period
"per_year" = "/年";
"per_month" = "/月";

// MARK: - Settings Menu
"about" = "このアプリについて";

// MARK: - Sunrise Sunset
"sunrise" = "日の出";
"sunset" = "日の入り";
"sunset_sunrise" = "日の出と日の入り";

// MARK: - Paywall Continue
"continue_using" = "続ける";

// MARK: - Google Geocoding Errors
"network_error" = "ネットワークエラー";
"invalid_response" = "無効なレスポンスです";
"api_error" = "検索キーワードを追加してください";
"decoding_error" = "データ解析エラー";
"no_search_results" = "検索結果が見つかりませんでした";

// MARK: - Settings Page Titles
"temperature_unit_setting" = "温度の単位";
"time_format_setting" = "時刻の表示形式";
"theme_setting" = "テーマ設定";
"weather_source_setting" = "情報提供元";

// MARK: - Theme Settings
"theme_system" = "システム";
"theme_light" = "ライトテーマ";
"theme_dark" = "ダークテーマ";

// MARK: - Temperature Units with Symbols
"celsius_with_symbol" = "摂氏 (°C)";
"fahrenheit_with_symbol" = "華氏 (°F)";

// MARK: - Weather Sources
"apple_weather" = "Apple Weather";
"google_weather" = "Google Weather";
"central_weather_administration" = "中央気象署 (台湾)";

// MARK: - Widget Settings
"widget_settings" = "ウィジェット設定";

// MARK: - What's New
"whats_new" = "新機能";
"show_whats_new" = "新機能を表示";
"release_notes" = "リリースノート";
"version" = "バージョン";
"no_updates_available" = "利用可能なアップデートはありません";

// MARK: - Purchase & Subscription Alerts
"purchase_failed" = "購入に失敗しました";
"subscription_success" = "登録完了";
"thank_you_for_subscribing" = "ご登録ありがとうございます！";
"error" = "エラー";
"package_not_available" = "このプランは現在利用できません";
"cannot_get_user_information" = "ユーザー情報を取得できませんでした";
"restore_failed" = "復元に失敗しました";
"restore_success" = "復元しました";
"purchase_restored" = "購入情報が復元されました";
"no_restorable_items" = "復元できる購入情報がありません";
"no_restorable_items_message" = "復元できる購入情報が見つかりませんでした。";

// MARK: - Paywall Carousel
"paywall_forecasts_120hr_title" = "すべての予報機能にアクセス";
"paywall_forecasts_120hr_subtitle" = "長期予報と、詳細な気象情報をチェック";
"paywall_saved_50_locations_title" = "好みの地点や施設を最大5カ所まで登録できる";
"paywall_saved_50_locations_subtitle" = "無料版では1地点のみ";
"paywall_home_widget_title" = "ホーム画面ウィジェット";
"paywall_home_widget_subtitle" = "プレミアム限定機能です";
"paywall_night_theme_title" = "ダークテーマ";
"paywall_night_theme_subtitle" = "無料版はライトテーマのみ利用可能";
"paywall_switch_provider_title" = "情報提供元の切り替え"; // 調整：更簡潔
"paywall_switch_provider_subtitle" = "プレミアム限定機能です";

// Unknown weather
"weather_unknown" = "不明";

// MARK: - Measurement System
"metric_system" = "メートル法";
"imperial_system" = "ヤード・ポンド法";
"measurement_system_setting" = "単位系";

// MARK: - Wind Speed Units
"wind_speed_ms" = "m/s";
"wind_speed_mph" = "mph";

// MARK: - Distance Units
"distance_km" = "km";
"distance_mi" = "mi";

// MARK: - Precipitation Units
"precipitation_mm" = "mm";
"precipitation_in" = "in";

// MARK: - Pressure Units
"pressure_hpa" = "hPa";
"pressure_inhg" = "inHg";

// MARK: - Weather Detail Labels
"feels_like" = "体感温度";
"humidity" = "湿度";
"precipitation_probability" = "降水確率";
"cloudiness" = "雲の量";
"uv_index" = "紫外線指数";
"daily_max_uv_index" = "日中の最高UV指数";
"wind_speed" = "風速";
"wind_gust" = "最大瞬間風速";
"visibility" = "視程距離";
"sea_pressure" = "海面気圧";
"ground_pressure" = "地上気圧";
"rain_volume" = "降水量";
"snow_volume" = "降雪量";

// MARK: - Weather Providers
"weather_providers_info" = "情報提供元について";
"more_providers_info" = "情報提供元について詳しく →";
"different_weather_models_info" = "気象モデルによって、予報期間や観測地点のデータが異なります。";
"weather_provider_apple" = "Apple天気";
"weather_provider_apple_subtitle" = "全世界に対応\n120時間分の予報";
"weather_provider_cwa" = "中央気象署";
"weather_provider_cwa_subtitle" = "台湾限定\n72時間分の予報";
"weather_provider_google" = "Google天気";
"weather_provider_google_subtitle" = "日本、韓国は非対応\n120時間分の予報";
"weather_provider_openweather" = "OpenWeather";
"weather_provider_openweather_subtitle" = "全世界に対応\n120時間分の予報";

// MARK: - Weather Sources (This section has many official names, kept them as is)
"weather_source_jma" = "気象庁";
"weather_source_eccc" = "カナダ環境・気候変動省";
"weather_source_dwd" = "ドイツ気象庁";
"weather_source_nws_noaa" = "アメリカ国立気象局 (NWS/NOAA)";
"weather_source_metoffice_ecmwf" = "英国気象庁 / 欧州中期予報センター (ECMWF)";
"weather_source_weather_com" = "The Weather Channel";
"weather_source_cwb" = "中央気象署 (台湾)";
"weather_source_environment_canada" = "カナダ環境省";
"weather_source_eumetnet" = "EUMETNET (欧州気象業務ネットワーク)";
"weather_source_ecmwf" = "欧州中期予報センター (ECMWF)";
"weather_source_noaa" = "アメリカ海洋大気庁 (NOAA)";
"weather_source_metoffice" = "英国気象庁";
"weather_source_gem_cmc" = "GEM (カナダ気象センター)";

// MARK: - Weather Data Status
"maintenance_status" = "メンテナンス中";
