/* 
  Localizable.strings (British English)
  MinimalistWeather
*/

// MARK: - Onboarding
"welcome" = "Welcome";
"onboarding_title_before_begin" = "Before You Begin";
"terms_privacy" = "Terms & Privacy";
"privacy_policy" = "Privacy Policy";
"terms_of_service" = "Terms of Service";
"accept" = "Got It, Continue";
"location" = "Locations";
"add_forecasts_city" = "Add a Location";
"searching" = "Searching...";
"confirm_add" = "Confirm Add";
"add_more_cities" = "Add More Locations";
"choose_your_plan" = "Choose Your Plan";
"feature_plans_available" = "Feature plans will be available here";
"coming_soon" = "Coming soon...";
"continue" = "Continue";
"all_set" = "All Set!";
"ready_to_start" = "You're ready to start using Minimalist Weather";
"get_started" = "Get Started";

// MARK: - Main App
"no_connection" = "NO CONNECTION";
"check_internet_connection" = "Please check your internet connection";
"feedback" = "Feedback";

// MARK: - FAQ
"faq_title" = "Frequently Asked Questions";
"loading_faq" = "Loading FAQ...";
"error_loading_faq" = "Error loading FAQ";
"retry" = "Retry";

// MARK: - Weather
"server_error" = "Connection timed out";
"send_feedback_check_announcements" = "Please try reloading, switching the provider, or check the status.";
"status_check" = "Check Status";

// MARK: - Settings
"unit" = "UNIT";
"pro" = "Pro Settings";
"language" = "Language";
"time_format" = "Time Format";
"12_hour_format" = "12-Hour Format";
"24_hour_format" = "24-Hour Format";

// MARK: - Location Search
"search_city_name" = "Enter location or address";
"no_locations_matched" = "NO LOCATIONS MATCHED";
"search_for_a_city" = "SEARCH FOR A CITY";
"no_saved_locations" = "NO SAVED LOCATIONS";
"unlimited_locations_available" = "Unlimited locations available.";
"upgrade_to_pro_add_more" = "Upgrade to Pro to add more locations.";

// MARK: - Paywall
"thanks_for_pro" = "Thanks for upgrading to Premium! We'd love to hear your ideas.";
"pro_description" = "If you have any questions or suggestions, feel free to let us know — together, we can create a better minimal experience.";
"unlock_full_experience" = "Unlock the full minimal experience";
"5_day_forecast" = "5-Day Forecast";
"multiple_locations" = "Multiple Locations";
"all_future_features" = "All Future Features Included";
"purchasing" = "Purchasing...";
"upgrade" = "Upgrade";
"restoring" = "Restoring...";
"restore" = "Restore";
"purchase_agreement" = "Terms | Privacy Policy";
"terms" = "Terms";
"loading" = "LOADING";

// MARK: - Common
"ok" = "OK";
"cancel" = "Cancel";
"search" = "Search";
"close" = "Close";
"add" = "Add";
"delete" = "Delete";
"edit" = "Edit";
"retry" = "Reload";
"auto_retrying" = "Reloading...";
"change_weather_source" = "Change Provider";

// MARK: - Time
"am" = "AM";
"pm" = "PM";
"now" = "NOW";

// MARK: - Weekdays
"monday" = "MON";
"tuesday" = "TUE";
"wednesday" = "WED";
"thursday" = "THU";
"friday" = "FRI";
"saturday" = "SAT";
"sunday" = "SUN";

// MARK: - Weather Units
"celsius" = "°C";
"fahrenheit" = "°F";
"percent" = "%";

// MARK: - Feature Plans
"current_weather_forecast" = "Current Weather Forecast";
"3_hour_2_day_forecast" = "48-Hour Forecast";
"1_location_forecast" = "1 Location Forecast";
"3_hour_5_day_forecast" = "5-Day Weather Forecast";
"2_location_forecast" = "50-Location Forecast";
"detailed_weather_info" = "Detailed Weather Data";
"custom_night_theme" = "Custom Night Theme";
"no_ads" = "No Ads";
"start_free" = "Start Free";
"start_7_day_trial" = "Start 3-Day Free Trial";
"monthly_plan" = "Monthly";
"yearly_plan" = "Yearly -50%";

// MARK: - Alerts
"no_connection_alert" = "No Connection";
"connect_internet_message" = "Please connect to the internet for weather updates.";

// MARK: - IAP Upgrade
"upgrade_to_pro" = "Upgrade to Pro";
"upgrade_now" = "Upgrade Now";
"upgrade_message_multiple_locations" = "Upgrade to Pro to add multiple locations and enjoy more features.";

// MARK: - Setup Completion Alert
"please_complete_setup" = "Please complete setup";
"complete_setup_before_using" = "You need to complete setup before using this feature";
"go_to_setup" = "Go to Setup";

// MARK: - Subscription Period
"per_year" = "/yr";
"per_month" = "/mo";

// MARK: - Settings Menu
"about" = "ABOUT";

// MARK: - Sunrise Sunset
"sunrise" = "Sunrise";
"sunset" = "Sunset";
"sunset_sunrise" = "Sunset & Sunrise";

// MARK: - Paywall Continue
"continue_using" = "Continue Using";

// MARK: - Google Geocoding Errors
"network_error" = "Network connection error";
"invalid_response" = "Invalid response";
"api_error" = "Please try a different or more specific location";
"decoding_error" = "Could not read data";
"no_search_results" = "No search results found";

// MARK: - Settings Page Titles
"temperature_unit_setting" = "Temperature Unit";
"time_format_setting" = "Time Format";
"theme_setting" = "Theme Setting";
"weather_source_setting" = "Weather Provider";

// MARK: - Theme Settings
"theme_system" = "System";
"theme_light" = "Light Theme";
"theme_dark" = "Dark Theme";

// MARK: - Temperature Units with Symbols
"celsius_with_symbol" = "Celsius °C";
"fahrenheit_with_symbol" = "Fahrenheit °F";

// MARK: - Weather Sources
"apple_weather" = "Apple Weather";
"google_weather" = "Google Weather";
"central_weather_administration" = "Central Weather Administration";

// MARK: - Widget Settings
"widget_settings" = "Widget Settings";

// MARK: - What's New
"whats_new" = "What's New";
"show_whats_new" = "Show What's New";
"release_notes" = "Release Notes";
"version" = "Version";
"no_updates_available" = "No updates available";

// MARK: - Purchase & Subscription Alerts
"purchase_failed" = "Purchase Failed";
"subscription_success" = "Subscription Success";
"thank_you_for_subscribing" = "Thank you for subscribing!";
"error" = "Error";
"package_not_available" = "Package not available";
"cannot_get_user_information" = "Cannot get user information";
"restore_failed" = "Restore Failed";
"restore_success" = "Restore Success";
"purchase_restored" = "Your purchase has been restored";
"no_restorable_items" = "No Restorable Items";
"no_restorable_items_message" = "We couldn't find any previous purchases to restore.";

// MARK: - Paywall Carousel
"paywall_forecasts_120hr_title" = "Unlock the Full Forecast";
"paywall_forecasts_120hr_subtitle" = "Long-range forecasts & detailed data.";
"paywall_saved_50_locations_title" = "Saved 50 Locations";
"paywall_saved_50_locations_subtitle" = "Free version is limited to 1 location.";
"paywall_home_widget_title" = "Home Widget";
"paywall_home_widget_subtitle" = "Not available in the free version.";
"paywall_night_theme_title" = "Dark Theme";
"paywall_night_theme_subtitle" = "Dark Theme is a premium feature.";
"paywall_switch_provider_title" = "Switch Provider";
"paywall_switch_provider_subtitle" = "Not available in the free version.";

// Unknown weather
"weather_unknown" = "Unknown Weather";

// MARK: - Measurement System
"metric_system" = "Metric";
"imperial_system" = "Imperial";
"measurement_system_setting" = "Measurement System";

// MARK: - Wind Speed Units
"wind_speed_ms" = "m/s";
"wind_speed_mph" = "mph";

// MARK: - Distance Units
"distance_km" = "km";
"distance_mi" = "mi";

// MARK: - Precipitation Units
"precipitation_mm" = "mm";
"precipitation_in" = "in";

// MARK: - Pressure Units
"pressure_hpa" = "hPa";
"pressure_inhg" = "inHg";

// MARK: - Weather Detail Labels
"feels_like" = "Feels Like";
"humidity" = "Humidity";
"precipitation_probability" = "Precipitation";
"cloudiness" = "Clouds";
"uv_index" = "UV Index";
"daily_max_uv_index" = "Daily Max UV Index";
"wind_speed" = "Wind Speed";
"wind_gust" = "Gust";
"visibility" = "Visibility";
"sea_pressure" = "Sea Pressure";
"ground_pressure" = "Ground Pressure";
"rain_volume" = "Rain";
"snow_volume" = "Snow";

// MARK: - Weather Providers
"weather_providers_info" = "Provider Information";
"more_providers_info" = "More Provider Info →";
"different_weather_models_info" = "Different weather models offer different forecast times and weather stations.";
"weather_provider_apple" = "Apple Weather";
"weather_provider_apple_subtitle" = "Supports all countries\n120-hour forecast";
"weather_provider_cwa" = "Central Weather Administration";
"weather_provider_cwa_subtitle" = "Only supports Taiwan\n72-hour forecast";
"weather_provider_google" = "Google Weather";
"weather_provider_google_subtitle" = "Does not support Japan or Korea\n120-hour forecast";
"weather_provider_openweather" = "OpenWeather";
"weather_provider_openweather_subtitle" = "Supports all countries\n120-hour forecast";

// MARK: - Weather Sources
"weather_source_jma" = "Japan Meteorological Agency";
"weather_source_eccc" = "Environment and Climate Change Canada";
"weather_source_dwd" = "Deutscher Wetterdienst";
"weather_source_nws_noaa" = "National Weather Service";
"weather_source_metoffice_ecmwf" = "The Met Office/European Centre for Medium-range Weather Forecasts (ECMWF)";
"weather_source_weather_com" = "Weather";
"weather_source_cwb" = "Weather Forecast Center";
"weather_source_environment_canada" = "Environment Canada";
"weather_source_eumetnet" = "EUMETNET";
"weather_source_ecmwf" = "European Centre for Medium-range Weather Forecasts (ECMWF)";
"weather_source_noaa" = "National Oceanic and Atmospheric Administration (NOAA)";
"weather_source_metoffice" = "Met Office";
"weather_source_gem_cmc" = "GEM (CMC, Canadian Meteorological Centre)";

// MARK: - Weather Data Status
"maintenance_status" = "Under Maintenance";
